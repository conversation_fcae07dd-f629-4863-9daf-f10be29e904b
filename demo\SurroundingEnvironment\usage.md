---
title: 园区周边环境
order: 2
---

本 Demo 演示园区周边环境功能的基础用法。

```jsx
import React, { Component } from 'react';
import ReactDOM from 'react-dom';
import SurroundingEnvironment from '@/pages/surroundingEnvironment';

class App extends Component {
  render() {
    return (
      <div>
        <SurroundingEnvironment />
      </div>
    );
  }
}

ReactDOM.render((
  <App />
), mountNode);
```

## 功能特性

### 主要功能
- **环境监测数据管理**: 展示园区周边各类环境监测数据
- **多维度筛选**: 支持按环境类型、监测区域、时间范围、环境状态等条件筛选
- **实时状态显示**: 通过颜色标签直观显示环境状态和风险等级
- **详情查看**: 点击查看按钮可查看详细的环境监测信息
- **历史数据追踪**: 在详情页面可查看设备的历史监测数据

### 数据字段说明
- **环境类型**: 空气质量、水质、噪声、土壤等环境监测类型
- **监测区域**: 具体的监测区域位置
- **监测指标**: 具体的监测参数（如PM2.5、PM10、SO2等）
- **监测值**: 当前监测到的数值
- **标准值**: 对应的环境标准限值
- **环境状态**: 正常、轻度污染、中度污染、重度污染
- **风险等级**: 低风险、中风险、高风险
- **监测设备**: 执行监测的设备信息

### 状态标识
- **环境状态颜色**:
  - 绿色: 正常
  - 橙色: 轻度污染
  - 红色: 中度污染
  - 紫色: 重度污染

- **风险等级颜色**:
  - 绿色: 低风险
  - 橙色: 中风险
  - 红色: 高风险

## API 接口

### 主要接口
1. `querySurroundingEnvList`: 查询园区周边环境记录列表
2. `querySurroundingEnvDetail`: 查询园区周边环境详情
3. `queryEnvironmentHistoryData`: 查询环境监测历史数据

### 数据结构
```typescript
// 园区周边环境记录
interface SurroundingEnvRecord {
  id: string;
  environmentType: string;
  monitorArea: string;
  monitorIndex: string;
  monitorValue: number;
  standardValue: number;
  environmentStatus: string;
  riskLevel: string;
  equipmentName: string;
  monitorTime: string;
  // ... 其他字段
}

// 环境详情信息
interface EnvironmentDetail {
  equipmentInfo: {
    equipmentId: string;
    equipmentName: string;
    equipmentCode: string;
    // ... 其他设备信息
  };
  monitorData: {
    currentValue: number;
    standardValue: number;
    exceedMultiple: number;
    // ... 其他监测数据
  };
  environmentAssessment: {
    qualityLevel: string;
    pollutionLevel: string;
    impactScope: string;
    suggestedMeasures: string;
  };
}
```

## 配置说明

### 字典配置
- `A22A08A08`: 环境类型字典
- `A22A09`: 环境状态字典
- `A22A10`: 风险等级字典

### 模块配置
在 `build.json` 中已配置模块名称为 `SurroundingEnvironmentList`，可在快开平台中直接使用。

## 使用注意事项

1. 确保后端API接口已正确配置和部署
2. 字典数据需要在系统中预先配置
3. 监测设备数据需要实时同步到系统中
4. 建议定期清理历史数据以保证系统性能
