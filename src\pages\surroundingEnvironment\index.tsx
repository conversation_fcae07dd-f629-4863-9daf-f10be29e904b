import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, Tag } from 'antd';
import surroundingEnvApi from '@/service/surroundingEnvApi';
import type { ApiResponse, SurroundingEnvRecord, SurroundingEnvQueryParams } from '@/service/surroundingEnvApi';
import locales from '@/locales';
import formApi from '@/service/formApi';
import moment from 'moment';
import SurroundingEnvModal from './components/SurroundingEnvModal';

// 筛选条件类型
type filterType = {
  environmentType?: { code: string; text: string }[];
  monitorArea?: string;
  monitorTime_end?: string;
  monitorTime_start?: string;
  environmentStatus?: { code: string; text: string }[];
};

/**
 * @description 园区周边环境管理页面
 * @returns
 */
const SurroundingEnvironmentList: React.FC = () => {
  const ref: React.MutableRefObject<ActionType> = useRef<ActionType>();
  const aa: ActionType = YTHList.createAction();
  const [dataObj, setDataObj] = useState<SurroundingEnvRecord>();
  const [editMenuVisiable, setEditMenuVisiable] = useState<boolean>(false); // 标记弹窗是否显示

  /**
   * 关闭弹窗
   */
  const closeModal: () => void = () => {
    setEditMenuVisiable(false);
    setDataObj(undefined);
  };

  /**
   * 处理筛选条件
   */
  const handleFilter = (filter: filterType): SurroundingEnvQueryParams => {
    const condition: SurroundingEnvQueryParams = {};

    if (filter.environmentType && filter.environmentType.length > 0) {
      condition.environmentType = filter.environmentType.map((item) => item.code).join(',');
    }

    if (filter.monitorArea) {
      condition.monitorArea = filter.monitorArea;
    }

    if (filter.monitorTime_start) {
      condition.startTime = moment(filter.monitorTime_start).format('YYYY-MM-DD');
    }

    if (filter.monitorTime_end) {
      condition.endTime = moment(filter.monitorTime_end).format('YYYY-MM-DD');
    }

    if (filter.environmentStatus && filter.environmentStatus.length > 0) {
      condition.environmentStatus = filter.environmentStatus.map((item) => item.code).join(',');
    }

    return condition;
  };

  /**
   * 获取环境状态标签颜色
   */
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'A22A09A01': // 正常
        return 'green';
      case 'A22A09A02': // 轻度污染
        return 'orange';
      case 'A22A09A03': // 中度污染
        return 'red';
      case 'A22A09A04': // 重度污染
        return 'purple';
      default:
        return 'default';
    }
  };

  /**
   * 获取风险等级标签颜色
   */
  const getRiskLevelColor = (riskLevel: string): string => {
    switch (riskLevel) {
      case 'A22A10A01': // 低风险
        return 'green';
      case 'A22A10A02': // 中风险
        return 'orange';
      case 'A22A10A03': // 高风险
        return 'red';
      default:
        return 'default';
    }
  };

  /**
   * 表格列配置
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'environmentType',
      title: '环境类型',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A22A08A08', // 环境类型字典
            },
            currentPage: 0,
            pageSize: 0,
          });
          return list;
        },
        p_props: {
          placeholder: '请选择环境类型',
        },
      },
      render: (value: string, record: SurroundingEnvRecord) => {
        return record.environmentTypeText || '-';
      },
    },
    {
      dataIndex: 'monitorArea',
      title: '监测区域',
      width: 150,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入监测区域',
      },
      render: (value: string, record: SurroundingEnvRecord) => {
        return record.monitorAreaText || value || '-';
      },
    },
    {
      dataIndex: 'monitorIndex',
      title: '监测指标',
      width: 120,
      display: true,
      render: (value: string, record: SurroundingEnvRecord) => {
        return record.monitorIndexText || '-';
      },
    },
    {
      dataIndex: 'monitorValue',
      title: '监测值',
      width: 100,
      display: true,
      render: (value: number, record: SurroundingEnvRecord) => {
        return `${value || 0} ${record.measureUnit || ''}`;
      },
    },
    {
      dataIndex: 'standardValue',
      title: '标准值',
      width: 100,
      display: true,
      render: (value: number, record: SurroundingEnvRecord) => {
        return `${value || 0} ${record.measureUnit || ''}`;
      },
    },
    {
      dataIndex: 'environmentStatus',
      title: '环境状态',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const { list } = await formApi.getDictionary({
            condition: {
              fatherCode: 'A22A09', // 环境状态字典
            },
            currentPage: 0,
            pageSize: 0,
          });
          return list;
        },
        p_props: {
          placeholder: '请选择环境状态',
        },
      },
      render: (value: string, record: SurroundingEnvRecord) => {
        return (
          <Tag color={getStatusColor(value)}>
            {record.environmentStatusText || '-'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'riskLevel',
      title: '风险等级',
      width: 100,
      display: true,
      render: (value: string, record: SurroundingEnvRecord) => {
        return (
          <Tag color={getRiskLevelColor(value)}>
            {record.riskLevelText || '-'}
          </Tag>
        );
      },
    },
    {
      dataIndex: 'equipmentName',
      title: '监测设备',
      width: 150,
      display: true,
    },
    {
      dataIndex: 'monitorTime',
      title: '监测时间',
      width: 160,
      query: true,
      display: true,
      componentName: 'DatePicker',
      queryMode: 'group',
      componentProps: {
        precision: 'day',
        formatter: 'YYYY-MM-DD',
        placeholder: '请选择监测时间',
      },
      render: (value: string) => {
        return value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
  ];

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="surroundingEnvironmentList"
        action={aa}
        searchMemory
        actionRef={ref}
        showRowSelection={false}
        operation={[]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ApiResponse<SurroundingEnvRecord[]> = await surroundingEnvApi.querySurroundingEnvList({
            aescs: [],
            descs: [],
            condition: handleFilter(filter),
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={70}
        rowOperation={(row: SurroundingEnvRecord) => {
          return [
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setDataObj(row);
                    setEditMenuVisiable(true);
                  }}
                >
                  查看
                </Button>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        title="园区周边环境详情"
        width="80%"
        footer={null}
        destroyOnClose
        onCancel={closeModal}
        maskClosable={false}
        visible={editMenuVisiable}
        key="surrounding-environment-modal"
      >
        <SurroundingEnvModal closeModal={closeModal} dataObj={dataObj} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(SurroundingEnvironmentList, locales, YTHLocalization.getLanguage());
