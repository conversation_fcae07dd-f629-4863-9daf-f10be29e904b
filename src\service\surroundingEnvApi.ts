import { envRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 分页查询参数接口
 */
interface PageQueryParams<T = unknown> {
  aescs?: string[];
  descs?: string[];
  condition: T | Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 园区周边环境记录查询参数接口
 */
export interface SurroundingEnvQueryParams {
  /** 环境类型 */
  environmentType?: string;
  /** 监测区域 */
  monitorArea?: string;
  /** 监测时间开始 */
  startTime?: string;
  /** 监测时间结束 */
  endTime?: string;
  /** 环境状态 */
  environmentStatus?: string;
}

/**
 * 园区周边环境记录数据接口
 */
export interface SurroundingEnvRecord {
  /** 主键 */
  id: string;
  /** 环境类型 */
  environmentType: string;
  /** 环境类型文本 */
  environmentTypeText?: string;
  /** 监测区域 */
  monitorArea: string;
  /** 监测区域文本 */
  monitorAreaText?: string;
  /** 监测指标 */
  monitorIndex: string;
  /** 监测指标文本 */
  monitorIndexText?: string;
  /** 监测值 */
  monitorValue: number;
  /** 计量单位 */
  measureUnit: string;
  /** 标准值 */
  standardValue: number;
  /** 环境状态 */
  environmentStatus: string;
  /** 环境状态文本 */
  environmentStatusText?: string;
  /** 监测时间 */
  monitorTime: string;
  /** 监测设备ID */
  equipmentId: string;
  /** 监测设备名称 */
  equipmentName: string;
  /** 风险等级 */
  riskLevel: string;
  /** 风险等级文本 */
  riskLevelText?: string;
  /** 备注 */
  remarks?: string;
  /** 序号 */
  serialNo?: number;
}

/**
 * 环境监测详情信息数据接口
 */
export interface EnvironmentDetail {
  /** 主键 */
  id: string;
  /** 监测设备信息 */
  equipmentInfo: {
    /** 设备ID */
    equipmentId: string;
    /** 设备名称 */
    equipmentName: string;
    /** 设备编号 */
    equipmentCode: string;
    /** 设备类型 */
    equipmentType: string;
    /** 设备状态 */
    equipmentStatus: string;
    /** 安装位置 */
    installLocation: string;
  };
  /** 监测数据 */
  monitorData: {
    /** 当前值 */
    currentValue: number;
    /** 标准值 */
    standardValue: number;
    /** 超标倍数 */
    exceedMultiple: number;
    /** 监测时间 */
    monitorTime: string;
    /** 数据状态 */
    dataStatus: string;
  };
  /** 环境评估 */
  environmentAssessment: {
    /** 环境质量等级 */
    qualityLevel: string;
    /** 污染程度 */
    pollutionLevel: string;
    /** 影响范围 */
    impactScope: string;
    /** 建议措施 */
    suggestedMeasures: string;
  };
}

/**
 * 园区周边环境 API 接口定义
 */
interface SurroundingEnvApiInterface {
  /** 查询园区周边环境记录列表 */
  querySurroundingEnvList: (
    data: PageQueryParams<SurroundingEnvQueryParams>,
  ) => Promise<ApiResponse<SurroundingEnvRecord[]>>;
  /** 查询园区周边环境详情 */
  querySurroundingEnvDetail: (
    data: { id: string },
  ) => Promise<ApiResponse<EnvironmentDetail>>;
  /** 查询环境监测历史数据 */
  queryEnvironmentHistoryData: (
    data: PageQueryParams<{ equipmentId: string; startTime: string; endTime: string }>,
  ) => Promise<ApiResponse<SurroundingEnvRecord[]>>;
}

/**
 * 园区周边环境模块 API
 */
const surroundingEnvApi: SurroundingEnvApiInterface = {
  /** 查询园区周边环境记录列表 */
  querySurroundingEnvList: (data) => {
    return envRequest.post('/surroundingEnvironment/page', { data });
  },

  /** 查询园区周边环境详情 */
  querySurroundingEnvDetail: (data) => {
    return envRequest.post('/surroundingEnvironment/detail', { data });
  },

  /** 查询环境监测历史数据 */
  queryEnvironmentHistoryData: (data) => {
    return envRequest.post('/surroundingEnvironment/historyData', { data });
  },
};

export default surroundingEnvApi;
