.spin-wrap {
  width: 100%;
  height: 100%;

  .ant-spin-container {
    width: 100%;
    height: 100%;
  }
}

.cesium-viewer-fullscreen-container {
  display: none !important;
}

.ant-card-body {
  padding-top: 0;
}

.map-container {
  height: 100%;
  width: 100%;
  position: relative;

  .toolbar {
    display: flex;
    justify-content: flex-start;
    padding-top: 10px;
    padding-right: 10px;
    padding-bottom: 10px;
    padding-left: 10px;
    flex-wrap: wrap;
    z-index: 1;
    position: absolute;
    align-content: flex-start;
    pointer-events: none;
    top: 0;
    left: 0;
    right: 0;

    .input-bar {
      margin-top: 12px;
      margin-left: 32px;
      z-index: 1;
      pointer-events: all;
    }

    .result-panel {
      width: 320px;
      max-height: 350px;
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;
      pointer-events: all;

      &.ant-card {
        background-color: rgb(255 255 255 / 10%);

        .ant-select-selector {
          background-color: rgb(255 255 255 / 70%);
        }

        .ant-card-head {
          padding-top: 0;
          padding-right: 12px;
          padding-bottom: 0;
          padding-left: 12px;
        }

        .ant-card-head-title {
          padding-top: 12px;
          padding-right: 0;
          padding-bottom: 12px;
          padding-left: 0;
        }
      }

      .search-bar {
        display: flex;
        align-items: center;

        .text {
          text-overflow: ellipsis;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
        }
      }

      .result-list.ant-list-loading {
        min-height: 100px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .info {
      padding-top: 12px;
      padding-right: 12px;
      padding-bottom: 0;
      padding-left: 12px;
      background-color: rgb(255 255 255 / 0%);
      border-radius: 2px;
      border-width: 0;
      border-style: solid;
      border-color: transparent;
      pointer-events: all;

      .item {
        margin-bottom: 12px;
        display: flex;
        align-items: center;

        .search-bar {
          justify-content: space-around;
          margin-left: 20px;
          display: flex;
          flex-direction: column;
          align-items: center;

          .search-bar-input {
            display: flex;
            justify-content: space-between;

            .input-area {
              min-width: 250px;
            }

            .map-search-btn {
              margin-left: 5px;
            }

            .map-clear-btn {
              margin-left: 5px;
            }
          }

          .search-bar-result {
            position: absolute;
            width: 30%;
            top: 40px;
            left: 30px;
            background-color: rgb(0 0 0 / 60%);
            margin-bottom: 10px;
            padding-left: 10px;
            text-align: left;
            color: #fff;
          }
        }

        .edit:hover {
          cursor: pointer;
        }

        .color {
          width: 20px;
          height: 20px;
          border-radius: 50%;
        }
      }

      .ant-input {
        background-color: rgb(255 255 255 / 90%);
        height: 40px;
        border-radius: 5px;
      }

      .address-text {
        margin-bottom: 0;
        color: rgb(0 0 0 / 100%);
        max-width: 210px;
      }

      input[type="number"] {
        appearance: textfield;
      }

      input[type="number"]::-webkit-inner-spin-button,
      input[type="number"]::-webkit-outer-spin-button {
        appearance: none;
        margin-top: 0;
        margin-right: 0;
        margin-bottom: 0;
        margin-left: 0;
      }
    }
  }

  .color-area {
    z-index: 1;
    left: 20%;
    bottom: 20px;
    position: absolute;
    display: flex;
    align-items: center;

    .reset-button {
      margin-left: 10px;
      height: 100%;
    }
  }

  .button-area {
    height: 200px;
    width: 60px;
    z-index: 1;
    bottom: 0;
    right: 20px;
    position: absolute;

    .button-use {
      width: 60px;
      height: 60px;
      border-radius: 60px;
      text-align: center;
      line-height: 60px;
      margin-top: 20px;
    }

    .button-use:hover {
      cursor: pointer;
    }

    .cancel-button {
      background-color: rgb(221 197 41 / 100%);
    }

    .confirm-button {
      background-color: rgb(22 155 213 / 100%);
      color: aliceblue;
    }
  }
}

.map-popup-container {
  width: 60vw;
  height: 60vh;
}
